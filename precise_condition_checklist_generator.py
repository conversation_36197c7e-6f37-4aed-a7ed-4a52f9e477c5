#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精确的条件落实情况表生成器
基于表格行列结构进行精确定位替换，避免错误替换
"""

import docx
from docx.enum.text import WD_COLOR_INDEX
import sqlite3
from pathlib import Path
import logging
from datetime import datetime
import shutil
import re

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PreciseConditionChecklistGenerator:
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.template_path = self.project_root / "templates" / "contract_disbursement" / "disbursement_condition_checklist_blueprint.docx"
        self.db_path = self.project_root / "database" / "enterprise_service.db"
        self.output_dir = self.project_root / "test_output"
        self.extracted_text_file = self.project_root / "extracted_original_text.txt"
        
        # 颜色定义
        self.green_color = WD_COLOR_INDEX.BRIGHT_GREEN  # 浅绿色 - 自动填充
        self.yellow_color = WD_COLOR_INDEX.YELLOW       # 黄色 - 等待填写
        
    def generate_checklist(self, company_id, support_amount=None):
        """生成条件落实情况表"""
        logger.info(f"🏦 开始生成精确条件落实情况表，企业ID: {company_id}")
        
        try:
            # 1. 获取企业数据
            company_data = self._get_company_data(company_id)
            if not company_data:
                raise ValueError(f"未找到企业数据: {company_id}")
            
            # 2. 获取申报书原文数据
            original_text_data = self._load_original_text()
            
            # 3. 准备输出目录
            self.output_dir.mkdir(exist_ok=True)
            
            # 4. 复制模板文件
            output_path = self.output_dir / f"精确条件落实情况表_{company_data['company_name']}.docx"
            shutil.copy2(self.template_path, output_path)
            
            # 5. 加载文档
            doc = docx.Document(output_path)
            
            # 6. 精确替换 - 基于表格结构
            replacement_count = self._precise_table_replacement(doc, company_data, original_text_data, support_amount)
            
            # 7. 保存文件
            doc.save(output_path)
            
            logger.info(f"✅ 精确条件落实情况表生成完成: {output_path}")
            return output_path, self._generate_summary(company_data, support_amount, replacement_count)
            
        except Exception as e:
            logger.error(f"❌ 生成失败: {e}")
            raise
    
    def _get_company_data(self, company_id):
        """从数据库获取企业数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT 
                    company_name, unified_social_credit_code, legal_representative,
                    registration_date, registered_capital, business_scope, business_description,
                    contact_phone, finance_manager_name, finance_manager_phone,
                    total_assets, total_liabilities, spouse_name, environmental_classification
                FROM companies 
                WHERE id = ?
            """, (company_id,))
            
            result = cursor.fetchone()
            conn.close()
            
            if result:
                return {
                    'company_name': result[0],
                    'unified_social_credit_code': result[1],
                    'legal_representative': result[2],
                    'registration_date': result[3],
                    'registered_capital': result[4],
                    'business_scope': result[5],
                    'business_description': result[6],
                    'contact_phone': result[7],
                    'finance_manager_name': result[8],
                    'finance_manager_phone': result[9],
                    'total_assets': result[10],
                    'total_liabilities': result[11],
                    'spouse_name': result[12],
                    'environmental_classification': result[13]
                }
            return None
            
        except Exception as e:
            logger.error(f"获取企业数据失败: {e}")
            return None
    
    def _load_original_text(self):
        """加载申报书原文数据"""
        try:
            if not self.extracted_text_file.exists():
                logger.warning("申报书原文文件不存在，将跳过原文填充")
                return {}
            
            with open(self.extracted_text_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 解析原文数据
            original_data = {}
            sections = content.split('----------------------------------------')
            
            for i in range(0, len(sections), 2):
                if i + 1 < len(sections):
                    key_section = sections[i].strip()
                    content_section = sections[i + 1].strip()
                    
                    # 提取键名
                    if ':' in key_section:
                        key = key_section.split(':')[0].strip()
                        if key.startswith('�'):
                            key = key[1:].strip()
                        original_data[key] = content_section
            
            logger.info(f"✅ 加载申报书原文数据: {len(original_data)}个条目")
            return original_data
            
        except Exception as e:
            logger.error(f"加载申报书原文失败: {e}")
            return {}
    
    def _precise_table_replacement(self, doc, company_data, original_data, support_amount):
        """基于表格结构进行精确替换"""
        logger.info("🎯 开始精确表格替换...")
        
        replacement_count = 0
        
        # 确保文档有表格
        if not doc.tables:
            logger.warning("文档中没有找到表格")
            return replacement_count
        
        # 获取主表格（第一个表格）
        main_table = doc.tables[0]
        
        logger.info(f"📊 主表格结构: {len(main_table.rows)}行 x {len(main_table.columns)}列")
        
        # 精确替换各个字段
        replacement_count += self._replace_row_1_date(main_table, company_data)           # 行1: 填报日期
        replacement_count += self._replace_row_2_company_info(main_table, company_data)   # 行2: 企业信息和编号
        replacement_count += self._replace_row_3_project_name(main_table, company_data)   # 行3: 项目名称
        replacement_count += self._replace_row_7_support_amount(main_table, support_amount) # 行7: 支用金额
        replacement_count += self._replace_row_9_conditions(main_table, original_data)    # 行9: 申报书条件
        replacement_count += self._replace_row_13_guarantee(main_table, company_data)     # 行13: 担保措施
        
        logger.info(f"📊 精确替换完成: 总计{replacement_count}个字段")
        return replacement_count
    
    def _replace_row_1_date(self, table, company_data):
        """替换行1列2: 填报日期"""
        try:
            cell = table.rows[0].cells[1]  # 行1列2
            
            # 查找日期模式并替换
            for paragraph in cell.paragraphs:
                text = paragraph.text
                if "2025年3月" in text:
                    now = datetime.now()
                    new_text = text.replace("2025年3月", f"{now.year}年{now.month:02d}月")
                    paragraph.text = new_text
                    
                    # 标记为绿色（年月自动生成）
                    for run in paragraph.runs:
                        if f"{now.year}年{now.month:02d}月" in run.text:
                            run.font.highlight_color = self.green_color
                    
                    logger.info(f"   ✅ 行1列2 填报日期: {now.year}年{now.month:02d}月")
                    return 1
            
            return 0
        except Exception as e:
            logger.error(f"替换填报日期失败: {e}")
            return 0
    
    def _replace_row_2_company_info(self, table, company_data):
        """替换行2: 企业信息和审批编号"""
        count = 0
        
        try:
            # 行2列1: 借款人信息
            cell_1 = table.rows[1].cells[0]
            for paragraph in cell_1.paragraphs:
                original_text = paragraph.text
                
                # 替换企业全名
                if "成都中科卓尔智能科技集团有限公司" in original_text:
                    new_text = original_text.replace("成都中科卓尔智能科技集团有限公司", company_data['company_name'])
                    paragraph.text = new_text
                    self._highlight_text_in_paragraph(paragraph, company_data['company_name'], self.green_color)
                    count += 1
                    logger.info(f"   ✅ 行2列1 企业全名: {company_data['company_name']}")
                
                # 替换企业简称
                if '"中科卓尔"' in original_text:
                    company_short = self._get_company_short_name(company_data['company_name'])
                    new_text = paragraph.text.replace('"中科卓尔"', f'"{company_short}"')
                    paragraph.text = new_text
                    self._highlight_text_in_paragraph(paragraph, f'"{company_short}"', self.green_color)
                    count += 1
                    logger.info(f"   ✅ 行2列1 企业简称: {company_short}")
            
            # 行2列2: 审批结论文号
            cell_2 = table.rows[1].cells[1]
            for paragraph in cell_2.paragraphs:
                text = paragraph.text
                
                # 替换额度编号
                if "PIFU510000000N202407210" in text:
                    new_text = text.replace("PIFU510000000N202407210", "PIFU510000000N202407210")
                    paragraph.text = new_text
                    self._highlight_text_in_paragraph(paragraph, "PIFU510000000N202407210", self.green_color)
                    count += 1
                    logger.info(f"   ✅ 行2列2 额度编号: PIFU510000000N202407210")
                
                # 替换业务编号
                if "PIFU5100000002025N00G8" in text:
                    new_text = paragraph.text.replace("PIFU5100000002025N00G8", "KHED510488500202522805")
                    paragraph.text = new_text
                    self._highlight_text_in_paragraph(paragraph, "KHED510488500202522805", self.green_color)
                    count += 1
                    logger.info(f"   ✅ 行2列2 业务编号: KHED510488500202522805")
            
            return count
        except Exception as e:
            logger.error(f"替换企业信息失败: {e}")
            return count
    
    def _replace_row_3_project_name(self, table, company_data):
        """替换行3列1: 项目名称"""
        try:
            cell = table.rows[2].cells[0]
            
            for paragraph in cell.paragraphs:
                text = paragraph.text
                if "成都中科卓尔智能科技集团有限公司" in text:
                    new_text = text.replace("成都中科卓尔智能科技集团有限公司", company_data['company_name'])
                    paragraph.text = new_text
                    self._highlight_text_in_paragraph(paragraph, company_data['company_name'], self.green_color)
                    logger.info(f"   ✅ 行3列1 项目名称: {company_data['company_name']}流动资金贷款")
                    return 1
            
            return 0
        except Exception as e:
            logger.error(f"替换项目名称失败: {e}")
            return 0
    
    def _replace_row_7_support_amount(self, table, support_amount):
        """替换行7列1: 本次支用金额"""
        try:
            cell = table.rows[6].cells[0]  # 行7列1（索引6）
            
            for paragraph in cell.paragraphs:
                text = paragraph.text
                if "本次支用金额：        万元" in text:
                    if support_amount:
                        new_text = text.replace("本次支用金额：        万元", f"本次支用金额：{support_amount}万元")
                        paragraph.text = new_text
                        self._highlight_text_in_paragraph(paragraph, f"{support_amount}万元", self.green_color)
                        logger.info(f"   ✅ 行7列1 支用金额: {support_amount}万元")
                        return 1
                    else:
                        # 如果没有提供支用金额，标记为黄色等待填写
                        self._highlight_text_in_paragraph(paragraph, "        万元", self.yellow_color)
                        logger.info(f"   🟡 行7列1 支用金额: 等待填写")
                        return 0
            
            return 0
        except Exception as e:
            logger.error(f"替换支用金额失败: {e}")
            return 0
    
    def _replace_row_9_conditions(self, table, original_data):
        """替换行9: 申报书条件内容"""
        count = 0
        
        try:
            # 行9包含用信前提条件、持续条件等内容
            for col_idx in [0, 1]:  # 两列都需要处理
                cell = table.rows[8].cells[col_idx]  # 行9（索引8）
                
                # 替换用信前提条件
                if 'prerequisite_conditions_original' in original_data:
                    condition_text = original_data['prerequisite_conditions_original']
                    count += self._replace_condition_in_cell(cell, "用信前提条件", condition_text)
                
                # 替换持续条件
                if 'continuous_conditions_original' in original_data:
                    condition_text = original_data['continuous_conditions_original']
                    count += self._replace_condition_in_cell(cell, "持续条件", condition_text)
                
                # 替换管理条件
                if 'management_conditions_original' in original_data:
                    condition_text = original_data['management_conditions_original']
                    count += self._replace_condition_in_cell(cell, "管理条件", condition_text)
            
            return count
        except Exception as e:
            logger.error(f"替换申报书条件失败: {e}")
            return count
    
    def _replace_row_13_guarantee(self, table, company_data):
        """替换行13: 担保措施"""
        count = 0
        
        try:
            # 行13包含担保措施详细信息
            for col_idx in [0, 1]:  # 两列都需要处理
                cell = table.rows[12].cells[col_idx]  # 行13（索引12）
                
                for paragraph in cell.paragraphs:
                    text = paragraph.text
                    
                    # 替换法定代表人
                    if "杨伟" in text:
                        new_text = text.replace("杨伟", company_data['legal_representative'])
                        paragraph.text = new_text
                        self._highlight_text_in_paragraph(paragraph, company_data['legal_representative'], self.green_color)
                        count += 1
                        logger.info(f"   ✅ 行13 法定代表人: {company_data['legal_representative']}")
                    
                    # 替换配偶
                    if "王斯颖" in text and company_data.get('spouse_name'):
                        new_text = paragraph.text.replace("王斯颖", company_data['spouse_name'])
                        paragraph.text = new_text
                        self._highlight_text_in_paragraph(paragraph, company_data['spouse_name'], self.green_color)
                        count += 1
                        logger.info(f"   ✅ 行13 配偶: {company_data['spouse_name']}")
                    
                    # 替换合同编号
                    if "建八卓尔保（2024）001号" in text:
                        new_contract = "建八卓尔保（2025）001号"
                        new_text = paragraph.text.replace("建八卓尔保（2024）001号", new_contract)
                        paragraph.text = new_text
                        self._highlight_text_in_paragraph(paragraph, new_contract, self.green_color)
                        count += 1
                        logger.info(f"   ✅ 行13 保证合同: {new_contract}")
                    
                    if "建八卓尔专质（2024）001号" in text:
                        new_contract = "建八卓尔专质（2025）001号"
                        new_text = paragraph.text.replace("建八卓尔专质（2024）001号", new_contract)
                        paragraph.text = new_text
                        self._highlight_text_in_paragraph(paragraph, new_contract, self.green_color)
                        count += 1
                        logger.info(f"   ✅ 行13 质押合同: {new_contract}")
            
            return count
        except Exception as e:
            logger.error(f"替换担保措施失败: {e}")
            return count
    
    def _replace_condition_in_cell(self, cell, condition_type, condition_text):
        """在单元格中替换特定条件内容"""
        try:
            for paragraph in cell.paragraphs:
                if condition_type in paragraph.text:
                    # 在条件标题后添加原文内容
                    if len(condition_text) > 50:  # 确保是完整内容
                        # 创建新段落添加原文
                        new_paragraph = cell.add_paragraph()
                        new_paragraph.text = condition_text
                        
                        # 标记为绿色
                        for run in new_paragraph.runs:
                            run.font.highlight_color = self.green_color
                        
                        logger.info(f"   ✅ 添加{condition_type}原文: {len(condition_text)}字符")
                        return 1
            
            return 0
        except Exception as e:
            logger.error(f"替换{condition_type}失败: {e}")
            return 0
    
    def _get_company_short_name(self, company_name):
        """获取企业简称"""
        if '中科卓尔' in company_name:
            return '中科卓尔'
        elif '神光' in company_name:
            return '神光'
        else:
            # 提取公司名称中的关键词
            import re
            match = re.search(r'([^（）]+)(?:有限公司|集团|科技)', company_name)
            if match:
                return match.group(1)[-4:]  # 取最后4个字符
            return company_name[:4]  # 默认取前4个字符
    
    def _highlight_text_in_paragraph(self, paragraph, text_to_highlight, color):
        """在段落中高亮特定文本"""
        try:
            for run in paragraph.runs:
                if text_to_highlight in run.text:
                    run.font.highlight_color = color
        except Exception as e:
            logger.error(f"高亮文本失败: {e}")
    
    def _generate_summary(self, company_data, support_amount, replacement_count):
        """生成填充摘要"""
        summary = {
            'company_name': company_data['company_name'],
            'auto_filled_fields': replacement_count,
            'manual_fields_needed': 1 if not support_amount else 0,
            'completion_rate': '100%' if support_amount else '95%',
            'missing_fields': [] if support_amount else ['本次支用金额']
        }
        return summary

def main():
    """测试函数"""
    print("🎯 精确条件落实情况表生成器测试")
    print("="*50)
    
    generator = PreciseConditionChecklistGenerator()
    
    # 测试中科卓尔（包含支用金额）
    company_id = "a1b2c3d4-e5f6-7890-1234-567890abcdef"
    support_amount = 1300  # 1300万元
    
    try:
        output_path, summary = generator.generate_checklist(company_id, support_amount)
        
        print(f"✅ 生成成功!")
        print(f"📁 输出文件: {output_path}")
        print(f"🏢 企业名称: {summary['company_name']}")
        print(f"📊 精确替换: {summary['auto_filled_fields']} 个字段")
        print(f"📈 完成率: {summary['completion_rate']}")
        print(f"💰 支用金额: {support_amount}万元")
        
        if summary['missing_fields']:
            print(f"⚠️ 缺失字段: {', '.join(summary['missing_fields'])}")
        else:
            print("🎉 所有字段已完成精确填充！")
        
    except Exception as e:
        print(f"❌ 生成失败: {e}")

if __name__ == "__main__":
    main()
