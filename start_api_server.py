#!/usr/bin/env python3
"""
启动API服务器
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from api.app import app
    print("✅ 成功导入API应用")
    
    print("🚀 启动API服务器...")
    print("📍 访问地址:")
    print("  - API根路径: http://localhost:5000")
    print("  - 前端界面: http://localhost:5000/frontend")
    print("  - 健康检查: http://localhost:5000/health")
    print("  - 公司列表: http://localhost:5000/api/companies")
    
    app.run(host='0.0.0.0', port=5000, debug=True)
    
except ImportError as e:
    print(f"❌ 导入API应用失败: {e}")
    print("请检查api目录和相关依赖")
except Exception as e:
    print(f"❌ 启动API服务器失败: {e}")
    import traceback
    traceback.print_exc()
