<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业服务驾驶舱 - 智能化流程管理系统</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/cockpit.css">
</head>
<body>
    <div class="cockpit-container">
        <!-- 页面标题 -->
        <header class="cockpit-header">
            <h1>🏢 企业服务驾驶舱</h1>
            <p class="subtitle">请选择客户并进入对应的业务模块</p>
        </header>

        <!-- 主要内容区域：驾驶舱布局 -->
        <main class="cockpit-main">
            <!-- 左侧：客户信息背景板 -->
            <aside class="customer-sidebar" id="customer-sidebar" style="display: none;">
                <div class="sidebar-header">
                    <h2>👤 当前客户</h2>
                    <button id="change-customer-btn" class="change-btn">更换</button>
                </div>
                
                <!-- 客户核心信息卡片 -->
                <div class="customer-card">
                    <div class="customer-name" id="sidebar-company-name">-</div>
                    <div class="customer-code" id="sidebar-credit-code">-</div>
                </div>

                <!-- 当前模块信息 -->
                <div class="current-module-section">
                    <div class="module-header">
                        <h3>🎯 当前模块</h3>
                        <button id="change-module-btn" class="change-btn">更换模块</button>
                    </div>
                    <div class="module-card">
                        <div class="module-name" id="sidebar-module-name">未选择模块</div>
                        <div class="module-description" id="sidebar-module-description">请先选择要使用的业务模块</div>
                    </div>
                </div>

                <!-- 客户详细信息 -->
                <div class="customer-details">
                    <div class="detail-item">
                        <span class="detail-label">📍 注册地址</span>
                        <span class="detail-value" id="sidebar-registered-address">-</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">📮 通讯地址</span>
                        <span class="detail-value" id="sidebar-communication-address">-</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">💼 业务简介</span>
                        <span class="detail-value" id="sidebar-business-description">-</span>
                    </div>
                </div>

                <!-- 关键人员 -->
                <div class="personnel-compact">
                    <h4>👥 关键人员</h4>
                    <div id="sidebar-personnel-list" class="personnel-compact-list">
                        <!-- 动态生成 -->
                    </div>
                </div>

                <!-- 企业标签 -->
                <div class="tags-compact">
                    <h4>🏷️ 企业标签</h4>
                    <div id="sidebar-company-tags" class="tags-compact-container">
                        <!-- 动态生成 -->
                    </div>
                </div>
            </aside>

            <!-- 中央：业务办理舞台 -->
            <section class="business-stage">
                <!-- 客户选择界面（初始状态） -->
                <div id="customer-selection-stage" class="selection-stage">
                    <div class="selection-content">
                        <div class="selection-icon">🎯</div>
                        <h2>选择您要服务的客户</h2>
                        <p>请从下方列表中选择一个客户，开始业务办理流程</p>
                        
                        <div class="customer-selector-enhanced">
                            <select id="customer-select" class="form-select-enhanced">
                                <option value="">请选择客户...</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- 模块选择舞台（客户选定后显示） -->
                <div id="module-selection-stage" class="module-selection-stage" style="display: none;">
                    <div class="stage-header">
                        <h2>🎯 选择业务模块</h2>
                        <p class="stage-subtitle">请选择要使用的业务模块</p>
                    </div>

                    <div class="modules-grid">
                        <!-- 银企直联模块 -->
                        <div class="module-card" data-module="yingqi_zhilian">
                            <div class="module-icon">🏦</div>
                            <h3 class="module-title">银企直联</h3>
                            <p class="module-description">银行系统与企业财务系统直连服务，包含协议生成、OA正文等功能</p>
                            <div class="module-features">
                                <span class="feature-tag">服务协议</span>
                                <span class="feature-tag">OA正文</span>
                                <span class="feature-tag">授权书</span>
                            </div>
                            <button class="module-select-btn" onclick="window.safeCockpitManager.selectModule('yingqi_zhilian')">
                                选择此模块
                            </button>
                        </div>

                        <!-- 客户接洽模块 -->
                        <div class="module-card" data-module="customer_engagement">
                            <div class="module-icon">🤝</div>
                            <h3 class="module-title">客户接洽与资料准备</h3>
                            <p class="module-description">客户接洽流程管理，包含检查清单、管护权确认函、服务方案等</p>
                            <div class="module-features">
                                <span class="feature-tag">检查清单</span>
                                <span class="feature-tag">管护权确认函</span>
                                <span class="feature-tag">服务方案</span>
                            </div>
                            <button class="module-select-btn" onclick="window.safeCockpitManager.selectModule('customer_engagement')">
                                选择此模块
                            </button>
                        </div>

                        <!-- 协定存款模块 -->
                        <div class="module-card" data-module="deposit_services">
                            <div class="module-icon">💰</div>
                            <h3 class="module-title">协定存款业务</h3>
                            <p class="module-description">协定存款协议生成与管理，包含协议生成、记录管理、配置设置等</p>
                            <div class="module-features">
                                <span class="feature-tag">协议生成</span>
                                <span class="feature-tag">记录管理</span>
                                <span class="feature-tag">业务配置</span>
                            </div>
                            <button class="module-select-btn" onclick="window.safeCockpitManager.selectModule('deposit_services')">
                                选择此模块
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 业务流程舞台（模块选定后显示） -->
                <div id="workflow-stage" class="workflow-stage" style="display: none;">
                    <div class="stage-header">
                        <h2>📋 业务办理清单</h2>
                        <div class="progress-enhanced">
                            <div class="progress-bar-enhanced">
                                <div id="progress-fill" class="progress-fill-enhanced"></div>
                            </div>
                            <span id="progress-text" class="progress-text-enhanced">0/4 已完成</span>
                        </div>
                    </div>

                    <div id="workflow-list" class="workflow-list-enhanced">
                        <!-- 业务办理项目 -->
                        <div class="workflow-item" data-task="service-agreement">
                            <div class="task-info">
                                <div class="task-checkbox">
                                    <input type="checkbox" id="task-agreement" class="task-check">
                                    <label for="task-agreement" class="check-label"></label>
                                </div>
                                <div class="task-content">
                                    <h3 class="task-title">生成《服务协议》</h3>
                                    <p class="task-description">为客户生成银企直联服务协议文档</p>
                                </div>
                            </div>
                            <div class="task-actions">
                                <button class="action-btn primary-action" data-action="generate-agreement">
                                    <span class="btn-icon">📄</span>
                                    <span class="btn-text">生成文档</span>
                                </button>
                            </div>
                        </div>

                        <div class="workflow-item" data-task="oa-document">
                            <div class="task-info">
                                <div class="task-checkbox">
                                    <input type="checkbox" id="task-oa" class="task-check">
                                    <label for="task-oa" class="check-label"></label>
                                </div>
                                <div class="task-content">
                                    <h3 class="task-title">生成《OA正文》</h3>
                                    <p class="task-description">使用AI协作生成OA办公文档</p>
                                </div>
                            </div>
                            <div class="task-actions">
                                <button class="action-btn ai-action" data-action="generate-oa">
                                    <span class="btn-icon">🤖</span>
                                    <span class="btn-text">AI协作</span>
                                </button>
                            </div>
                        </div>

                        <div class="workflow-item completed" data-task="authorization-form">
                            <div class="task-info">
                                <div class="task-checkbox">
                                    <input type="checkbox" id="task-auth" class="task-check" checked>
                                    <label for="task-auth" class="check-label"></label>
                                </div>
                                <div class="task-content">
                                    <h3 class="task-title">下载《对公客户授权及承诺书》</h3>
                                    <p class="task-description">标准PDF表单，已预先准备</p>
                                </div>
                            </div>
                            <div class="task-actions">
                                <button class="action-btn download-action" data-action="download-auth">
                                    <span class="btn-icon">📥</span>
                                    <span class="btn-text">下载PDF</span>
                                </button>
                            </div>
                        </div>

                        <div class="workflow-item completed" data-task="application-form">
                            <div class="task-info">
                                <div class="task-checkbox">
                                    <input type="checkbox" id="task-app" class="task-check" checked>
                                    <label for="task-app" class="check-label"></label>
                                </div>
                                <div class="task-content">
                                    <h3 class="task-title">下载《对公综合服务申请书》</h3>
                                    <p class="task-description">标准PDF表单，已预先准备</p>
                                </div>
                            </div>
                            <div class="task-actions">
                                <button class="action-btn download-action" data-action="download-app">
                                    <span class="btn-icon">📥</span>
                                    <span class="btn-text">下载PDF</span>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 实时状态反馈 -->
                    <div class="status-feedback" id="status-feedback">
                        <div class="feedback-content">
                            <span class="feedback-icon">✨</span>
                            <span class="feedback-text">准备就绪，请开始办理业务</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 右侧：操作日志面板 -->
            <aside class="logs-panel" id="logs-panel" style="display: none;">
                <div class="panel-header">
                    <h3>📋 操作日志</h3>
                    <button id="clear-logs-btn" class="clear-btn">清空</button>
                </div>
                <div id="operation-logs" class="logs-container-enhanced">
                    <!-- 动态生成日志 -->
                </div>
            </aside>
        </main>

        <!-- OA工作台模态窗口 -->
        <div id="oa-workspace-modal" class="oa-workspace-modal" style="display: none;">
            <div class="oa-workspace-content">
                <!-- OA工作台内容将在这里动态加载 -->
            </div>
        </div>
    </div>

    <!-- 加载JavaScript -->
    <script src="js/api-client.js"></script>
    <script src="js/cockpit-manager-safe.js"></script>
</body>
</html>
