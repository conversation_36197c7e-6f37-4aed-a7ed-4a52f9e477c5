"""
企业信息核心库 API 服务
提供RESTful API接口用于管理企业信息
"""

from flask import Flask, request, jsonify, send_file
from flask_cors import CORS
import logging
import traceback
import io
import os
from typing import Dict, Any

from .services.company_service import CompanyService
from .services.advanced_document_service import AdvancedDocumentService, DocumentProcessingError
from .services.customer_engagement_service import CustomerEngagementService
from .services.deposit_service import DepositService
from .models import CompanyValidator, ValidationError

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 创建Flask应用
app = Flask(__name__)
CORS(app)  # 启用跨域支持

# 初始化服务
company_service = CompanyService()
customer_engagement_service = CustomerEngagementService()

# 获取数据库管理器（如果可用）
try:
    db_manager = company_service.db if hasattr(company_service, 'db') else None
    advanced_document_service = AdvancedDocumentService(db_manager)
    deposit_service = DepositService(db_manager)
except Exception as e:
    logger.warning(f"无法获取数据库管理器，使用模拟数据: {e}")
    advanced_document_service = AdvancedDocumentService(None)
    deposit_service = DepositService(None)

def create_response(data: Any = None, message: str = "success", status_code: int = 200) -> tuple:
    """创建标准化的API响应"""
    response = {
        "status": "success" if status_code < 400 else "error",
        "message": message,
        "data": data
    }
    return jsonify(response), status_code

def handle_error(error: Exception, status_code: int = 500) -> tuple:
    """处理错误并返回标准化错误响应"""
    error_message = str(error)
    logger.error(f"API错误: {error_message}")
    logger.error(traceback.format_exc())
    
    return create_response(
        data=None,
        message=error_message,
        status_code=status_code
    )

@app.route('/', methods=['GET'])
def index():
    """根路由 - 返回API信息"""
    return create_response({
        "name": "企业信息核心库 API",
        "version": "1.0",
        "endpoints": {
            "companies": "/api/companies",
            "health": "/health",
            "frontend": "/frontend"
        }
    }, "企业信息核心库 API 服务")

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查端点"""
    return create_response({"status": "healthy"}, "API服务运行正常")

@app.route('/api/companies', methods=['GET'])
def get_companies():
    """
    获取所有公司的列表
    返回ID和公司名称，用于前端下拉列表
    """
    try:
        companies = company_service.get_companies_list()
        return create_response(companies, f"获取公司列表成功，共 {len(companies)} 条记录")
    except Exception as e:
        return handle_error(e)

@app.route('/api/company/<company_id>', methods=['GET'])
def get_company_detail(company_id: str):
    """
    根据公司ID获取完整的公司信息
    返回动态生成的JSON结构，包含公司基本信息、人员关系和标签
    """
    try:
        company = company_service.get_company_detail(company_id)
        if company is None:
            return create_response(None, "公司不存在", 404)
        
        return create_response(company, "获取公司详细信息成功")
    except Exception as e:
        return handle_error(e)

@app.route('/api/companies', methods=['POST'])
def create_company():
    """
    创建新公司
    在事务中同时更新主表和历史表
    """
    try:
        # 验证请求数据
        if not request.is_json:
            return create_response(None, "请求必须是JSON格式", 400)
        
        data = request.get_json()
        if not data:
            return create_response(None, "请求数据不能为空", 400)
        
        # 验证输入数据
        validated_request = CompanyValidator.validate_create_request(data)
        
        # 创建公司
        company_id = company_service.create_company(validated_request)
        
        return create_response(
            {"company_id": company_id},
            "公司创建成功",
            201
        )
        
    except ValidationError as e:
        return create_response(None, str(e), 400)
    except ValueError as e:
        return create_response(None, str(e), 409)  # 冲突
    except Exception as e:
        return handle_error(e)

@app.route('/api/company/<company_id>', methods=['PUT'])
def update_company(company_id: str):
    """
    更新公司信息
    在事务中同时更新主表和历史表
    """
    try:
        # 验证请求数据
        if not request.is_json:
            return create_response(None, "请求必须是JSON格式", 400)
        
        data = request.get_json()
        if not data:
            return create_response(None, "请求数据不能为空", 400)
        
        # 验证输入数据
        validated_request = CompanyValidator.validate_update_request(data)
        
        # 更新公司
        success = company_service.update_company(company_id, validated_request)
        
        if success:
            return create_response(
                {"company_id": company_id},
                "公司信息更新成功"
            )
        else:
            return create_response(None, "没有需要更新的字段", 400)
        
    except ValidationError as e:
        return create_response(None, str(e), 400)
    except ValueError as e:
        return create_response(None, str(e), 404 if "不存在" in str(e) else 409)
    except Exception as e:
        return handle_error(e)

@app.route('/api/company/<company_id>/history', methods=['GET'])
def get_company_history(company_id: str):
    """
    获取公司的变更历史记录
    """
    try:
        history = company_service.get_company_history(company_id)
        return create_response(
            history,
            f"获取公司历史记录成功，共 {len(history)} 条记录"
        )
    except Exception as e:
        return handle_error(e)

@app.route('/api/templates', methods=['GET'])
def get_available_templates():
    """
    获取可用的模板列表
    """
    try:
        templates = document_service.get_available_templates()
        return create_response(templates, "获取模板列表成功")
    except Exception as e:
        return handle_error(e)

@app.route('/api/company/<company_id>/document/word/<template_name>', methods=['GET'])
def generate_word_document(company_id: str, template_name: str):
    """
    生成Word文档
    根据公司ID和模板名称生成填充后的Word文档
    """
    try:
        # 生成文档
        doc_bytes, filename = document_service.process_word_document(company_id, template_name)

        # 创建文件流
        doc_stream = io.BytesIO(doc_bytes)
        doc_stream.seek(0)

        # 返回文件下载
        return send_file(
            doc_stream,
            as_attachment=True,
            download_name=filename,
            mimetype='application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        )

    except DocumentProcessingError as e:
        return handle_error(e, 400)
    except Exception as e:
        return handle_error(e)

@app.route('/api/document/pdf/<template_name>', methods=['GET'])
def generate_pdf_document(template_name: str):
    """
    生成PDF文档
    处理PDF模板并返回填充后的文档
    """
    try:
        # 生成文档
        pdf_bytes, filename = document_service.process_pdf_document(template_name)

        # 创建文件流
        pdf_stream = io.BytesIO(pdf_bytes)
        pdf_stream.seek(0)

        # 返回文件下载
        return send_file(
            pdf_stream,
            as_attachment=True,
            download_name=filename,
            mimetype='application/pdf'
        )

    except DocumentProcessingError as e:
        return handle_error(e, 400)
    except Exception as e:
        return handle_error(e)

# 高级文档生成API（银企直联模块）
@app.route('/api/documents/generate_agreement', methods=['POST'])
def generate_agreement_document():
    """智能文档生成 - 服务协议"""
    try:
        data = request.get_json()
        company_id = data.get('company_id')

        if not company_id:
            return create_response(None, '缺少必要参数: company_id', 400)

        # 一键生成服务协议
        doc_buffer = advanced_document_service.generate_service_agreement(company_id)

        return send_file(
            doc_buffer,
            as_attachment=True,
            download_name=f"服务协议-{company_id}.docx",
            mimetype='application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        )

    except Exception as e:
        return handle_error(e)

@app.route('/api/documents/oa/prepare', methods=['POST'])
def prepare_oa_document_old():
    """准备OA文档工作台（旧版本）"""
    try:
        data = request.get_json()
        company_id = data.get('company_id')

        if not company_id:
            return create_response(None, '缺少必要参数: company_id', 400)

        result = advanced_document_service.prepare_oa_template(company_id)

        return create_response(result, 'OA文档工作台准备成功')

    except Exception as e:
        return handle_error(e)

@app.route('/api/documents/oa/complete', methods=['POST'])
def complete_oa_document():
    """完成OA文档生成"""
    try:
        data = request.get_json()
        company_id = data.get('company_id')
        semi_finished_content = data.get('semi_finished_content')
        gemini_response = data.get('gemini_response')

        if not all([company_id, semi_finished_content, gemini_response]):
            return create_response(None, '缺少必要参数', 400)

        doc_buffer = advanced_document_service.complete_oa_document(
            company_id, semi_finished_content, gemini_response
        )

        return send_file(
            doc_buffer,
            as_attachment=True,
            download_name=f"OA正文-{company_id}.docx",
            mimetype='application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        )

    except Exception as e:
        return handle_error(e)

@app.errorhandler(404)
def not_found(error):
    """处理404错误"""
    return create_response(None, "请求的资源不存在", 404)

@app.errorhandler(405)
def method_not_allowed(error):
    """处理405错误"""
    return create_response(None, "请求方法不被允许", 405)

@app.errorhandler(500)
def internal_error(error):
    """处理500错误"""
    return create_response(None, "服务器内部错误", 500)

# =====================================================
# 客户接洽与资料准备模块 API端点
# =====================================================

@app.route('/api/templates/customer_engagement/checklist', methods=['GET'])
def get_checklist_template():
    """
    下载融资资料清单模板
    """
    try:
        template_path = customer_engagement_service.get_checklist_template_path()

        if not os.path.exists(template_path):
            return create_response(None, "模板文件不存在", 404)

        return send_file(
            template_path,
            as_attachment=True,
            download_name="融资资料清单.docx",
            mimetype='application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        )

    except Exception as e:
        return handle_error(e)

@app.route('/api/documents/generate_service_plan', methods=['POST'])
def generate_service_plan():
    """
    生成定制化银行服务方案PPT（保持向后兼容）
    """
    try:
        data = request.get_json()
        if not data:
            return create_response(None, "请求数据不能为空", 400)

        company_id = data.get('company_id')
        if not company_id:
            return create_response(None, "缺少必要参数: company_id", 400)

        # 生成定制化PPT
        ppt_bytes, filename = customer_engagement_service.generate_service_plan_ppt(company_id)

        # 创建文件流
        from io import BytesIO
        ppt_stream = BytesIO(ppt_bytes)
        ppt_stream.seek(0)

        return send_file(
            ppt_stream,
            as_attachment=True,
            download_name=filename,
            mimetype='application/vnd.openxmlformats-officedocument.presentationml.presentation'
        )

    except ValueError as e:
        return create_response(None, str(e), 404)
    except Exception as e:
        return handle_error(e)

@app.route('/api/documents/generate_custodianship_letter', methods=['POST'])
def generate_custodianship_letter():
    """
    生成管护权确认函Word文档
    """
    try:
        data = request.get_json()
        if not data:
            return create_response(None, "请求数据不能为空", 400)

        company_id = data.get('company_id')
        if not company_id:
            return create_response(None, "缺少必要参数: company_id", 400)

        # 生成管护权确认函
        doc_bytes, filename = customer_engagement_service.generate_document(company_id, "custodianship_letter")

        # 创建文件流
        from io import BytesIO
        doc_stream = BytesIO(doc_bytes)
        doc_stream.seek(0)

        return send_file(
            doc_stream,
            as_attachment=True,
            download_name=filename,
            mimetype='application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        )

    except ValueError as e:
        return create_response(None, str(e), 404)
    except Exception as e:
        return handle_error(e)

@app.route('/api/documents/download_credit_checklist', methods=['GET'])
def download_credit_checklist():
    """
    下载授信资料清单静态模板文件
    """
    try:
        import os

        # 静态模板文件路径
        template_path = os.path.join(
            os.path.dirname(os.path.dirname(__file__)),
            "templates",
            "customer_engagement",
            "customer_engagement_checklist_template.docx"
        )

        # 检查文件是否存在
        if not os.path.exists(template_path):
            return create_response(None, "授信资料清单模板文件不存在", 404)

        return send_file(
            template_path,
            as_attachment=True,
            download_name="授信资料清单.docx",
            mimetype='application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        )

    except Exception as e:
        return handle_error(e)

@app.route('/api/documents/generate', methods=['POST'])
def generate_document():
    """
    通用文档生成接口，支持多种模板类型
    """
    try:
        data = request.get_json()
        if not data:
            return create_response(None, "请求数据不能为空", 400)

        company_id = data.get('company_id')
        template_type = data.get('template_type')

        if not company_id:
            return create_response(None, "缺少必要参数: company_id", 400)
        if not template_type:
            return create_response(None, "缺少必要参数: template_type", 400)

        # 生成文档
        doc_bytes, filename = customer_engagement_service.generate_document(company_id, template_type)

        # 创建文件流
        from io import BytesIO
        doc_stream = BytesIO(doc_bytes)
        doc_stream.seek(0)

        # 根据模板类型设置MIME类型
        if template_type == "service_plan":
            mimetype = 'application/vnd.openxmlformats-officedocument.presentationml.presentation'
        elif template_type == "custodianship_letter":
            mimetype = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        else:
            mimetype = 'application/octet-stream'

        return send_file(
            doc_stream,
            as_attachment=True,
            download_name=filename,
            mimetype=mimetype
        )

    except ValueError as e:
        return create_response(None, str(e), 404)
    except Exception as e:
        return handle_error(e)

@app.route('/api/company/<company_id>/documents', methods=['POST'])
def upload_company_document(company_id: str):
    """
    上传公司文档
    """
    try:
        # 检查是否有文件上传
        if 'file' not in request.files:
            return create_response(None, "没有文件被上传", 400)

        file = request.files['file']
        if file.filename == '':
            return create_response(None, "没有选择文件", 400)

        # 获取其他参数
        document_category = request.form.get('document_category', 'other_documents')
        description = request.form.get('description', '')
        uploaded_by = request.form.get('uploaded_by', 'system')

        # 获取文件大小
        file.seek(0, 2)  # 移动到文件末尾
        file_size = file.tell()
        file.seek(0)  # 重置到文件开头

        # 上传文件
        doc_id = customer_engagement_service.upload_company_document(
            company_id=company_id,
            file_data=file,
            original_filename=file.filename,
            file_size=file_size,
            document_category=document_category,
            description=description,
            uploaded_by=uploaded_by
        )

        return create_response(
            {"document_id": doc_id},
            "文档上传成功",
            201
        )

    except ValueError as e:
        return create_response(None, str(e), 404)
    except Exception as e:
        return handle_error(e)

@app.route('/api/company/<company_id>/documents', methods=['GET'])
def get_company_documents(company_id: str):
    """
    获取公司的所有文档列表
    """
    try:
        documents = customer_engagement_service.get_company_documents(company_id)

        return create_response(
            documents,
            f"获取文档列表成功，共 {len(documents)} 个文档"
        )

    except Exception as e:
        return handle_error(e)

@app.route('/api/document_categories', methods=['GET'])
def get_document_categories():
    """
    获取文档分类列表
    """
    try:
        categories = customer_engagement_service.get_document_categories()

        return create_response(
            categories,
            f"获取文档分类成功，共 {len(categories)} 个分类"
        )

    except Exception as e:
        return handle_error(e)

# =====================================================
# OA正文人机协作工作台 API端点
# =====================================================

@app.route('/api/documents/prepare_oa', methods=['POST'])
def prepare_oa_document():
    """
    准备OA文档的半成品版本，用于人机协作工作台
    """
    try:
        data = request.get_json()
        if not data:
            return create_response(None, "请求数据不能为空", 400)

        company_id = data.get('company_id')
        if not company_id:
            return create_response(None, "缺少必要参数: company_id", 400)

        # 生成半成品OA文档
        oa_data = advanced_document_service.prepare_oa_document(company_id)

        return create_response(
            oa_data,
            "OA半成品文档准备成功"
        )

    except ValueError as e:
        return create_response(None, str(e), 404)
    except Exception as e:
        return handle_error(e)

@app.route('/api/documents/finalize_oa', methods=['POST'])
def finalize_oa_document():
    """
    合成最终的OA文档
    """
    try:
        data = request.get_json()
        if not data:
            return create_response(None, "请求数据不能为空", 400)

        company_id = data.get('company_id')
        temp_doc_path = data.get('temp_doc_path')
        ai_content = data.get('ai_content', '')

        if not company_id or not temp_doc_path:
            return create_response(None, "缺少必要参数: company_id, temp_doc_path", 400)

        # 合成最终文档
        doc_buffer = advanced_document_service.finalize_oa_document(
            company_id, temp_doc_path, ai_content
        )

        # 获取公司信息用于文件名
        try:
            import requests
            company_response = requests.get(f"http://localhost:5000/api/company/{company_id}")
            if company_response.status_code == 200:
                company_data = company_response.json()["data"]
                company_name = company_data.get('company_name', '未知公司')
            else:
                company_name = '未知公司'
        except:
            company_name = '未知公司'

        return send_file(
            doc_buffer,
            as_attachment=True,
            download_name=f"{company_name}_OA正文.docx",
            mimetype='application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        )

    except ValueError as e:
        return create_response(None, str(e), 404)
    except Exception as e:
        return handle_error(e)

# ==================== 协定存款业务API ====================

@app.route('/api/deposit/agreements/<company_id>', methods=['GET'])
def get_company_deposit_agreements(company_id):
    """获取企业的协定存款协议列表"""
    try:
        result = deposit_service.get_company_agreements(company_id)
        if result['success']:
            return create_response(result['data'], "获取协议列表成功")
        else:
            return create_response(None, result['message'], 400)
    except Exception as e:
        return handle_error(e)

@app.route('/api/deposit/agreements/<company_id>/statistics', methods=['GET'])
def get_deposit_statistics(company_id):
    """获取企业的协定存款统计信息"""
    try:
        result = deposit_service.get_agreement_statistics(company_id)
        if result['success']:
            return create_response(result['data'], "获取统计信息成功")
        else:
            return create_response(None, result['message'], 400)
    except Exception as e:
        return handle_error(e)

@app.route('/api/deposit/agreements', methods=['POST'])
def create_deposit_agreement():
    """创建新的协定存款协议"""
    try:
        data = request.get_json()
        if not data or 'company_id' not in data:
            return create_response(None, "缺少必要参数", 400)

        # 使用我们测试过的规则：
        # - 存款金额默认1000万元
        # - 利率调整45bp
        # - 只填充企业名称和账户号，其他字段留空
        result = deposit_service.create_agreement(
            company_id=data['company_id'],
            deposit_amount=data.get('deposit_amount', 1000),
            interest_rate_adjustment=data.get('interest_rate_adjustment', 45),
            agreement_date=data.get('agreement_date'),
            effective_date=data.get('effective_date'),
            maturity_date=data.get('maturity_date'),
            status=data.get('status', 'draft')
        )

        if result['success']:
            return create_response(result['data'], result['message'])
        else:
            return create_response(None, result['message'], 400)

    except Exception as e:
        return handle_error(e)

@app.route('/api/deposit/generate', methods=['POST'])
def generate_deposit_agreement():
    """生成协定存款协议（简化版）"""
    try:
        data = request.get_json()
        if not data or 'company_id' not in data:
            return create_response(None, "缺少必要参数", 400)

        company_id = data['company_id']
        deposit_amount = data.get('deposit_amount', 1000)

        # 直接调用生成器
        from deposit_services.generators.agreed_deposit_generator import AgreedDepositGenerator
        generator = AgreedDepositGenerator()

        # 生成协议
        file_path, summary = generator.generate_deposit_agreement(company_id, deposit_amount)

        # 生成文件ID（用于下载）
        import uuid
        file_id = str(uuid.uuid4())

        # 存储文件路径映射（简单实现，实际应该用数据库）
        if not hasattr(app, 'file_mappings'):
            app.file_mappings = {}
        app.file_mappings[file_id] = str(file_path)

        return create_response({
            'file_id': file_id,
            'filename': file_path.name,
            'summary': summary
        }, "协议生成成功")

    except Exception as e:
        return handle_error(e)

@app.route('/api/deposit/download/<file_id>', methods=['GET'])
def download_deposit_agreement(file_id):
    """下载协定存款协议文件"""
    try:
        # 获取文件路径
        if not hasattr(app, 'file_mappings') or file_id not in app.file_mappings:
            return create_response(None, "文件不存在", 404)

        file_path = app.file_mappings[file_id]

        # 检查文件是否存在
        from pathlib import Path
        if not Path(file_path).exists():
            return create_response(None, "文件不存在", 404)

        # 发送文件
        return send_file(
            file_path,
            as_attachment=True,
            download_name=Path(file_path).name,
            mimetype='application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        )
    except Exception as e:
        return handle_error(e)

@app.route('/frontend')
@app.route('/frontend/')
def serve_frontend():
    """提供前端界面"""
    try:
        frontend_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'frontend', 'index_unified_cockpit.html')
        if os.path.exists(frontend_path):
            return send_file(frontend_path)
        else:
            return create_response(None, "前端界面文件不存在", 404)
    except Exception as e:
        return handle_error(e)

if __name__ == '__main__':
    logger.info("启动企业信息核心库 API 服务...")
    app.run(host='0.0.0.0', port=5000, debug=True)

    except Exception as e:
        return handle_error(e)

if __name__ == '__main__':
    logger.info("启动企业信息核心库API服务...")
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=True
    )
