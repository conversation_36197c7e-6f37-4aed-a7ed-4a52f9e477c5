<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>驾驶舱测试版</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/cockpit.css">
</head>
<body>
    <div class="cockpit-container">
        <header class="cockpit-header">
            <h1>🏦 银企直联业务驾驶舱（测试版）</h1>
            <p class="subtitle">智能化业务流程管理系统</p>
        </header>

        <main class="cockpit-main">
            <!-- 客户信息侧边栏 -->
            <aside class="customer-sidebar" id="customer-sidebar" style="display: none;">
                <div class="sidebar-header">
                    <h2>👤 当前客户</h2>
                    <button id="change-customer-btn" class="change-btn">更换</button>
                </div>
                
                <div class="customer-card">
                    <div class="customer-name" id="sidebar-company-name">-</div>
                    <div class="customer-code" id="sidebar-credit-code">-</div>
                </div>
            </aside>

            <!-- 业务舞台 -->
            <section class="business-stage">
                <!-- 客户选择界面 -->
                <div id="customer-selection-stage" class="selection-stage">
                    <div class="selection-content">
                        <div class="selection-icon">🎯</div>
                        <h2>选择您要服务的客户</h2>
                        <p>请从下方列表中选择一个客户，开始业务办理流程</p>
                        
                        <div class="customer-selector-enhanced">
                            <select id="customer-select" class="form-select-enhanced">
                                <option value="">请选择客户...</option>
                                <option value="test1">成都卫讯科技有限公司</option>
                                <option value="test2">测试企业2</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- 工作流程舞台 -->
                <div id="workflow-stage" class="workflow-stage" style="display: none;">
                    <div class="stage-header">
                        <h2>📋 业务办理清单</h2>
                        <div class="progress-enhanced">
                            <div class="progress-bar-enhanced">
                                <div id="progress-fill" class="progress-fill-enhanced"></div>
                            </div>
                            <span id="progress-text" class="progress-text-enhanced">0/4 已完成</span>
                        </div>
                    </div>

                    <div id="workflow-list" class="workflow-list-enhanced">
                        <div class="workflow-item" data-task="service-agreement">
                            <div class="task-info">
                                <div class="task-checkbox">
                                    <input type="checkbox" id="task-agreement" class="task-check">
                                    <label for="task-agreement" class="check-label"></label>
                                </div>
                                <div class="task-content">
                                    <h3 class="task-title">生成《服务协议》</h3>
                                    <p class="task-description">为客户生成银企直联服务协议文档</p>
                                </div>
                            </div>
                            <div class="task-actions">
                                <button class="action-btn primary-action" data-action="generate-agreement">
                                    <span class="btn-icon">📄</span>
                                    <span class="btn-text">生成文档</span>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="status-feedback" id="status-feedback">
                        <div class="feedback-content">
                            <span class="feedback-icon">✨</span>
                            <span class="feedback-text">准备就绪，请开始办理业务</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 操作日志面板 -->
            <aside class="logs-panel" id="logs-panel" style="display: none;">
                <div class="panel-header">
                    <h3>📋 操作日志</h3>
                    <button id="clear-logs-btn" class="clear-btn">清空</button>
                </div>
                <div id="operation-logs" class="logs-container-enhanced">
                    <!-- 动态生成日志 -->
                </div>
            </aside>
        </main>
    </div>

    <script>
        // 简化的驾驶舱管理器
        class SimpleCockpitManager {
            constructor() {
                this.currentCustomer = null;
                this.init();
            }

            init() {
                console.log('SimpleCockpitManager 初始化');
                this.bindEvents();
                this.addLog('info', '驾驶舱测试版已启动');
            }

            bindEvents() {
                // 客户选择事件
                const customerSelect = document.getElementById('customer-select');
                if (customerSelect) {
                    customerSelect.addEventListener('change', (e) => {
                        this.selectCustomer(e.target.value);
                    });
                }

                // 更换客户按钮
                const changeCustomerBtn = document.getElementById('change-customer-btn');
                if (changeCustomerBtn) {
                    changeCustomerBtn.addEventListener('click', () => {
                        this.showCustomerSelection();
                    });
                }

                // 工作流程按钮事件
                document.addEventListener('click', (e) => {
                    if (e.target.closest('.action-btn')) {
                        const btn = e.target.closest('.action-btn');
                        const action = btn.dataset.action;
                        this.handleTaskAction(action);
                    }
                });
            }

            selectCustomer(customerId) {
                if (!customerId) return;

                this.currentCustomer = {
                    id: customerId,
                    company_name: customerId === 'test1' ? '成都卫讯科技有限公司' : '测试企业2',
                    unified_social_credit_code: '915101003320526751'
                };

                this.showWorkflowStage();
                this.updateCustomerSidebar();
                this.addLog('success', `已选择客户: ${this.currentCustomer.company_name}`);
            }

            showWorkflowStage() {
                document.getElementById('customer-selection-stage').style.display = 'none';
                document.getElementById('workflow-stage').style.display = 'block';
                document.getElementById('customer-sidebar').style.display = 'block';
                document.getElementById('logs-panel').style.display = 'block';
            }

            showCustomerSelection() {
                document.getElementById('customer-selection-stage').style.display = 'block';
                document.getElementById('workflow-stage').style.display = 'none';
                document.getElementById('customer-sidebar').style.display = 'none';
                document.getElementById('logs-panel').style.display = 'none';
                this.currentCustomer = null;
                document.getElementById('customer-select').value = '';
            }

            updateCustomerSidebar() {
                if (!this.currentCustomer) return;
                document.getElementById('sidebar-company-name').textContent = this.currentCustomer.company_name;
                document.getElementById('sidebar-credit-code').textContent = this.currentCustomer.unified_social_credit_code;
            }

            handleTaskAction(action) {
                if (!this.currentCustomer) {
                    this.addLog('error', '请先选择客户');
                    return;
                }

                this.addLog('info', `正在执行操作: ${action}`);
                
                if (action === 'generate-agreement') {
                    this.addLog('success', '《服务协议》生成成功（测试模式）');
                    document.getElementById('task-agreement').checked = true;
                }
            }

            addLog(type, message) {
                const logsContainer = document.getElementById('operation-logs');
                if (!logsContainer) return;

                const logEntry = document.createElement('div');
                logEntry.className = `log-entry ${type}`;
                
                const timestamp = new Date().toLocaleTimeString();
                logEntry.innerHTML = `
                    <div style="font-weight: 500; margin-bottom: 0.2rem;">
                        ${timestamp} - ${this.getLogTypeIcon(type)}
                    </div>
                    <div>${message}</div>
                `;

                logsContainer.insertBefore(logEntry, logsContainer.firstChild);
            }

            getLogTypeIcon(type) {
                const icons = {
                    'success': '✅ 成功',
                    'error': '❌ 错误',
                    'info': 'ℹ️ 信息'
                };
                return icons[type] || 'ℹ️ 信息';
            }
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            try {
                window.simpleCockpitManager = new SimpleCockpitManager();
                console.log('驾驶舱测试版初始化成功');
            } catch (error) {
                console.error('初始化失败:', error);
                alert('驾驶舱初始化失败: ' + error.message);
            }
        });
    </script>
</body>
</html>
