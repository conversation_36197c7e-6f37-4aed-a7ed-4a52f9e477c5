/**
 * 统一驾驶舱管理器
 * 企业服务平台的统一入口和指挥中心
 */

class UnifiedCockpitManager {
    constructor() {
        this.currentCustomer = null;
        this.availableModules = {
            'bank-enterprise': {
                name: '银企直联',
                description: '银行系统与企业财务系统直连服务',
                workspaceUrl: 'index_cockpit.html',
                available: true
            },
            'customer-engagement': {
                name: '客户接洽与资料准备',
                description: '标准化工具分发和客户资料收集管理',
                workspaceUrl: 'customer_engagement_workspace.html',
                available: true
            },
            'deposit-services': {
                name: '协定存款业务',
                description: '协定存款协议生成与管理服务',
                workspaceUrl: 'deposit_services_workspace.html',
                available: true
            },
            'contract-disbursement': {
                name: '合同支用',
                description: '合同支用与放款管理服务',
                workspaceUrl: 'contract_disbursement_workspace.html',
                available: true
            },
            'credit-application': {
                name: '授信申请',
                description: '企业授信相关申请材料准备',
                workspaceUrl: null,
                available: false
            }
        };
        
        this.init();
    }

    init() {
        console.log('统一驾驶舱初始化开始');
        this.bindEvents();
        this.loadCustomers();
        this.addLog('info', '企业服务驾驶舱已启动');
    }

    bindEvents() {
        // 客户选择事件
        const customerSelect = document.getElementById('customer-select');
        if (customerSelect) {
            customerSelect.addEventListener('change', (e) => {
                this.selectCustomer(e.target.value);
            });
        }

        // 更换客户按钮
        const changeCustomerBtn = document.getElementById('change-customer-btn');
        if (changeCustomerBtn) {
            changeCustomerBtn.addEventListener('click', () => {
                this.resetToCustomerSelection();
            });
        }

        // 业务模块选择
        document.addEventListener('click', (e) => {
            // 处理业务模块卡片点击
            const moduleCard = e.target.closest('.business-module-card');
            if (moduleCard && !moduleCard.classList.contains('disabled') && !moduleCard.classList.contains('add-module')) {
                const moduleId = moduleCard.dataset.module;
                if (moduleId && this.availableModules[moduleId]?.available) {
                    this.selectBusinessModule(moduleId);
                }
            }

            // 处理进入办理按钮点击
            const bankEnterpriseBtn = e.target.closest('[data-action="enter-bank-enterprise"]');
            if (bankEnterpriseBtn) {
                e.preventDefault();
                this.selectBusinessModule('bank-enterprise');
            }

            // 处理客户接洽与资料准备模块按钮点击
            const customerEngagementBtn = e.target.closest('[data-action="enter-customer-engagement"]');
            if (customerEngagementBtn) {
                e.preventDefault();
                this.selectBusinessModule('customer-engagement');
            }

            // 处理协定存款业务模块按钮点击
            const depositServicesBtn = e.target.closest('[data-action="enter-deposit-services"]');
            if (depositServicesBtn) {
                e.preventDefault();
                this.selectBusinessModule('deposit-services');
            }

            // 处理合同支用模块按钮点击
            const contractDisbursementBtn = e.target.closest('[data-action="enter-contract-disbursement"]');
            if (contractDisbursementBtn) {
                e.preventDefault();
                this.selectBusinessModule('contract-disbursement');
            }
        });

        // 清空日志
        const clearLogsBtn = document.getElementById('clear-logs-btn');
        if (clearLogsBtn) {
            clearLogsBtn.addEventListener('click', () => {
                this.clearLogs();
            });
        }
    }

    async loadCustomers() {
        try {
            console.log('开始加载客户列表');
            
            let companies = [];
            
            // 尝试从API加载
            try {
                const response = await fetch('http://127.0.0.1:5000/api/companies');
                if (response.ok) {
                    const data = await response.json();
                    companies = data.data || data;
                    this.addLog('success', `成功加载 ${companies.length} 个客户`);
                } else {
                    throw new Error('API响应错误');
                }
            } catch (apiError) {
                console.log('API不可用，使用完整模拟数据');
                companies = [
                    { id: '34af7659-d69a-4c05-a697-6ae6eb00aad3', company_name: '成都卫讯科技有限公司' },
                    { id: 'a1b2c3d4-e5f6-7890-1234-567890abcdef', company_name: '成都中科卓尔智能科技集团有限公司' },
                    { id: 'a806f8c4-b681-4bed-90b3-cadf2170826f', company_name: '四川至臻精密光学有限公司' },
                    { id: '14371dda-2f8f-4d4c-82e6-c431dcf3b146', company_name: '神光光学集团有限公司' }
                ];
                this.addLog('warning', '使用完整模拟数据（4个客户）');
            }
            
            // 填充客户选择器
            const customerSelect = document.getElementById('customer-select');
            if (customerSelect) {
                customerSelect.innerHTML = '<option value="">请选择客户...</option>';
                companies.forEach(company => {
                    const option = document.createElement('option');
                    option.value = company.id;
                    option.textContent = company.company_name;
                    customerSelect.appendChild(option);
                });
            }
            
        } catch (error) {
            console.error('加载客户失败:', error);
            this.addLog('error', '加载客户失败: ' + error.message);
        }
    }

    async selectCustomer(customerId) {
        if (!customerId) return;

        try {
            this.addLog('info', '正在加载客户信息...');
            
            // 尝试从API获取客户详细信息
            let customerData = null;
            
            try {
                const response = await fetch(`http://127.0.0.1:5000/api/company/${customerId}`);
                if (response.ok) {
                    const apiData = await response.json();
                    if (apiData.status === 'success') {
                        customerData = apiData.data;
                    }
                }
            } catch (apiError) {
                console.log('API调用失败，使用基础数据');
            }
            
            // 如果API失败，使用基础数据
            if (!customerData) {
                const customerSelect = document.getElementById('customer-select');
                const selectedOption = customerSelect.querySelector(`option[value="${customerId}"]`);
                customerData = {
                    id: customerId,
                    company_name: selectedOption ? selectedOption.textContent : '未知企业',
                    unified_social_credit_code: '915101003320526751'
                };
            }
            
            this.currentCustomer = customerData;
            this.showBusinessSelection();
            this.updateCustomerInfo();
            this.addLog('success', `已选择客户: ${this.currentCustomer.company_name}`);
            
        } catch (error) {
            console.error('选择客户失败:', error);
            this.addLog('error', '选择客户失败: ' + error.message);
        }
    }

    showBusinessSelection() {
        // 隐藏客户选择界面
        const customerSelectionStage = document.getElementById('customer-selection-stage');
        if (customerSelectionStage) {
            customerSelectionStage.style.display = 'none';
        }

        // 显示业务选择舞台
        const businessSelectionStage = document.getElementById('business-selection-stage');
        if (businessSelectionStage) {
            businessSelectionStage.style.display = 'block';
        }

        // 显示操作日志
        const logsPanel = document.getElementById('logs-panel');
        if (logsPanel) {
            logsPanel.style.display = 'block';
        }
    }

    updateCustomerInfo() {
        if (!this.currentCustomer) return;

        // 更新业务选择舞台中的客户显示
        const customerDisplay = document.getElementById('current-customer-display');
        if (customerDisplay) {
            customerDisplay.textContent = this.currentCustomer.company_name;
        }
    }

    selectBusinessModule(moduleId) {
        if (!this.currentCustomer) {
            this.addLog('error', '请先选择客户');
            return;
        }

        const module = this.availableModules[moduleId];
        if (!module) {
            this.addLog('error', '未知的业务模块');
            return;
        }

        this.addLog('info', `正在进入${module.name}模块...`);

        if (moduleId === 'bank-enterprise') {
            this.enterBankEnterpriseModule();
        } else if (moduleId === 'customer-engagement') {
            this.enterCustomerEngagementModule();
        } else if (moduleId === 'deposit-services') {
            this.enterDepositServicesModule();
        } else if (moduleId === 'contract-disbursement') {
            this.enterContractDisbursementModule();
        } else {
            this.addLog('warning', `${module.name}模块正在开发中`);
        }
    }

    async enterBankEnterpriseModule() {
        try {
            this.addLog('info', `正在为 ${this.currentCustomer.company_name} 生成银企直联文档...`);

            const response = await fetch('http://127.0.0.1:5000/api/yingqi-zhilian/generate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    company_id: this.currentCustomer.id
                })
            });

            if (response.ok) {
                const result = await response.json();
                this.addLog('success', `银企直联文档生成成功: ${result.message}`);
            } else {
                throw new Error('文档生成失败');
            }

        } catch (error) {
            console.error('银企直联模块执行失败:', error);
            this.addLog('error', '银企直联模块执行失败: ' + error.message);
        }
    }

    enterCustomerEngagementModule() {
        try {
            // 构建客户接洽与资料准备工作台URL，传递客户信息并锁定客户
            const params = new URLSearchParams({
                customer_id: this.currentCustomer.id,
                customer_name: this.currentCustomer.company_name,
                customer_code: this.currentCustomer.unified_social_credit_code || '',
                from_cockpit: 'true',
                lock_customer: 'true'  // 锁定客户，不允许在工作台中重新选择
            });

            const workspaceUrl = `customer_engagement_workspace.html?${params.toString()}`;

            this.addLog('success', `正在为 ${this.currentCustomer.company_name} 进入客户接洽与资料准备工作台...`);

            // 跳转到客户接洽与资料准备工作台
            window.location.href = workspaceUrl;

        } catch (error) {
            console.error('进入客户接洽与资料准备模块失败:', error);
            this.addLog('error', '进入客户接洽与资料准备模块失败: ' + error.message);
        }
    }

    async enterDepositServicesModule() {
        try {
            this.addLog('info', `正在为 ${this.currentCustomer.company_name} 生成协定存款文档...`);

            const response = await fetch('http://127.0.0.1:5000/api/deposit-services/generate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    company_id: this.currentCustomer.id
                })
            });

            if (response.ok) {
                const result = await response.json();
                this.addLog('success', `协定存款文档生成成功: ${result.message}`);
            } else {
                throw new Error('文档生成失败');
            }

        } catch (error) {
            console.error('协定存款模块执行失败:', error);
            this.addLog('error', '协定存款模块执行失败: ' + error.message);
        }
    }

    async enterContractDisbursementModule() {
        try {
            this.addLog('info', `正在为 ${this.currentCustomer.company_name} 生成合同支用文档...`);

            const response = await fetch('http://127.0.0.1:5000/api/contract-disbursement/generate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    company_id: this.currentCustomer.id
                })
            });

            if (response.ok) {
                const result = await response.json();
                this.addLog('success', `合同支用文档生成成功: ${result.message}`);
            } else {
                throw new Error('文档生成失败');
            }

        } catch (error) {
            console.error('合同支用模块执行失败:', error);
            this.addLog('error', '合同支用模块执行失败: ' + error.message);
        }
    }

    resetToCustomerSelection() {
        // 重置到客户选择状态
        this.currentCustomer = null;

        // 显示客户选择界面
        const customerSelectionStage = document.getElementById('customer-selection-stage');
        if (customerSelectionStage) {
            customerSelectionStage.style.display = 'block';
        }

        // 隐藏业务选择舞台
        const businessSelectionStage = document.getElementById('business-selection-stage');
        if (businessSelectionStage) {
            businessSelectionStage.style.display = 'none';
        }

        // 隐藏日志面板
        const logsPanel = document.getElementById('logs-panel');
        if (logsPanel) {
            logsPanel.style.display = 'none';
        }

        // 重置客户选择器
        const customerSelect = document.getElementById('customer-select');
        if (customerSelect) {
            customerSelect.value = '';
        }

        // 滚动到顶部
        customerSelectionStage?.scrollIntoView({ behavior: 'smooth' });

        this.addLog('info', '已重置到客户选择状态');
    }

    addLog(type, message) {
        try {
            console.log(`[${type.toUpperCase()}] ${message}`);

            const logsContainer = document.getElementById('operation-logs');
            if (!logsContainer) return;

            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            
            const timestamp = new Date().toLocaleTimeString();
            logEntry.innerHTML = `
                <div style="font-weight: 500; margin-bottom: 0.2rem;">
                    ${timestamp} - ${this.getLogIcon(type)}
                </div>
                <div>${message}</div>
            `;

            logsContainer.insertBefore(logEntry, logsContainer.firstChild);

            // 限制日志数量
            const logs = logsContainer.querySelectorAll('.log-entry');
            if (logs.length > 15) {
                logs[logs.length - 1].remove();
            }
        } catch (error) {
            console.error('添加日志失败:', error);
        }
    }

    getLogIcon(type) {
        const icons = {
            success: '✅ 成功',
            error: '❌ 错误',
            info: 'ℹ️ 信息',
            warning: '⚠️ 警告'
        };
        return icons[type] || 'ℹ️ 信息';
    }

    clearLogs() {
        const logsContainer = document.getElementById('operation-logs');
        if (logsContainer) {
            logsContainer.innerHTML = '';
            this.addLog('info', '操作日志已清空');
        }
    }
}

// 初始化统一驾驶舱
document.addEventListener('DOMContentLoaded', () => {
    try {
        console.log('开始初始化统一驾驶舱');
        window.unifiedCockpitManager = new UnifiedCockpitManager();
        console.log('统一驾驶舱初始化成功');
    } catch (error) {
        console.error('统一驾驶舱初始化失败:', error);
        alert('驾驶舱启动失败: ' + error.message);
    }
});
