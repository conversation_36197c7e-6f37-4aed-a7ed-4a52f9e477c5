#!/usr/bin/env python3
"""
简单的API服务器 - 确保基本功能可用
"""

from flask import Flask, jsonify
from flask_cors import CORS
import sqlite3

app = Flask(__name__)
CORS(app)

@app.route('/')
def index():
    return jsonify({
        "status": "success",
        "message": "企业信息核心库 API 服务",
        "data": {"name": "简单API", "version": "1.0"}
    })

@app.route('/health')
def health():
    return jsonify({
        "status": "success", 
        "message": "API服务运行正常",
        "data": {"status": "healthy"}
    })

@app.route('/api/companies')
def get_companies():
    try:
        # 从数据库获取公司列表
        conn = sqlite3.connect('database/enterprise_service.db')
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT id, company_name 
            FROM companies 
            ORDER BY company_name
        """)
        
        companies = []
        for row in cursor.fetchall():
            companies.append({
                'id': row[0],
                'company_name': row[1]
            })
        
        conn.close()
        
        return jsonify({
            "status": "success",
            "message": f"获取公司列表成功，共 {len(companies)} 条记录",
            "data": companies
        })
        
    except Exception as e:
        return jsonify({
            "status": "error",
            "message": f"获取公司列表失败: {str(e)}",
            "data": None
        }), 500

if __name__ == '__main__':
    print("🚀 启动简单API服务器...")
    print("📍 访问地址:")
    print("  - 根路径: http://localhost:5000")
    print("  - 健康检查: http://localhost:5000/health") 
    print("  - 公司列表: http://localhost:5000/api/companies")
    
    app.run(host='0.0.0.0', port=5000, debug=True)
