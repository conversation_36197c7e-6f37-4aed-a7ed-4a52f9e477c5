#!/usr/bin/env python3
"""
专家级API连接诊断
"""

import requests
import json
from pathlib import Path

def expert_api_diagnosis():
    print('=== 专家级API连接诊断 ===\n')
    
    # 1. 测试API基础连接
    print('🔍 1. 测试API基础连接...')
    test_basic_api_connection()
    
    # 2. 检查前端配置
    print('\n🔍 2. 检查前端API配置...')
    check_frontend_api_config()
    
    # 3. 测试所有关键API端点
    print('\n🔍 3. 测试关键API端点...')
    test_all_api_endpoints()
    
    # 4. 检查CORS配置
    print('\n🔍 4. 检查CORS配置...')
    check_cors_configuration()
    
    # 5. 提供解决方案
    print('\n💡 5. 专家建议...')
    provide_expert_solutions()

def test_basic_api_connection():
    """测试API基础连接"""
    endpoints = [
        'http://localhost:5000',
        'http://127.0.0.1:5000', 
        'http://localhost:5000/health',
        'http://localhost:5000/api/companies'
    ]
    
    for endpoint in endpoints:
        try:
            response = requests.get(endpoint, timeout=5)
            print(f'  ✅ {endpoint} - 状态: {response.status_code}')
            if response.status_code == 200:
                data = response.json()
                print(f'     响应: {data.get("message", "无消息")}')
        except requests.exceptions.ConnectionError:
            print(f'  ❌ {endpoint} - 连接被拒绝')
        except requests.exceptions.Timeout:
            print(f'  ⏰ {endpoint} - 连接超时')
        except Exception as e:
            print(f'  ❌ {endpoint} - 错误: {e}')

def check_frontend_api_config():
    """检查前端API配置"""
    js_files = [
        'frontend/js/unified-cockpit-manager.js',
        'frontend/js/api-client.js',
        'frontend/js/app.js'
    ]
    
    for js_file in js_files:
        if Path(js_file).exists():
            print(f'  📄 检查 {js_file}...')
            with open(js_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
                # 查找API URL配置
                if 'localhost:5000' in content:
                    print(f'    ✅ 找到 localhost:5000 配置')
                if '127.0.0.1:5000' in content:
                    print(f'    ✅ 找到 127.0.0.1:5000 配置')
                if 'fetch(' in content:
                    print(f'    ✅ 找到 fetch 调用')
                    
                # 查找具体的API调用
                api_calls = []
                lines = content.split('\n')
                for i, line in enumerate(lines):
                    if 'fetch(' in line and ('api/' in line or 'localhost' in line):
                        api_calls.append(f'    行{i+1}: {line.strip()}')
                
                if api_calls:
                    print(f'    📡 API调用:')
                    for call in api_calls[:3]:  # 只显示前3个
                        print(call)
        else:
            print(f'  ❌ 文件不存在: {js_file}')

def test_all_api_endpoints():
    """测试所有关键API端点"""
    endpoints = {
        '公司列表': '/api/companies',
        '健康检查': '/health',
        '银企直联': '/api/yingqi-zhilian/generate',
        '协定存款': '/api/deposit-services/generate',
        '合同支用': '/api/contract-disbursement/generate',
        '客户接洽': '/api/customer-engagement/generate'
    }
    
    base_url = 'http://localhost:5000'
    
    for name, endpoint in endpoints.items():
        try:
            url = base_url + endpoint
            if endpoint in ['/health', '/api/companies']:
                # GET请求
                response = requests.get(url, timeout=5)
            else:
                # POST请求（模拟）
                test_data = {'company_id': 'test'}
                response = requests.post(url, json=test_data, timeout=5)
            
            print(f'  📡 {name} ({endpoint}): 状态 {response.status_code}')
            
        except requests.exceptions.ConnectionError:
            print(f'  ❌ {name} ({endpoint}): 连接被拒绝')
        except Exception as e:
            print(f'  ❌ {name} ({endpoint}): {str(e)[:50]}...')

def check_cors_configuration():
    """检查CORS配置"""
    try:
        # 模拟浏览器的CORS预检请求
        headers = {
            'Origin': 'file://',
            'Access-Control-Request-Method': 'POST',
            'Access-Control-Request-Headers': 'Content-Type'
        }
        
        response = requests.options('http://localhost:5000/api/companies', headers=headers, timeout=5)
        print(f'  📡 CORS预检请求: 状态 {response.status_code}')
        
        # 检查CORS响应头
        cors_headers = {
            'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
            'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
            'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers')
        }
        
        for header, value in cors_headers.items():
            if value:
                print(f'    ✅ {header}: {value}')
            else:
                print(f'    ❌ {header}: 未设置')
                
    except Exception as e:
        print(f'  ❌ CORS检查失败: {e}')

def provide_expert_solutions():
    """提供专家解决方案"""
    print('  🎯 问题诊断:')
    print('    1. 前端使用 file:// 协议访问，可能被CORS阻止')
    print('    2. API服务可能没有正确配置CORS')
    print('    3. 前端可能配置了错误的API地址')
    
    print('\n  🛠️ 解决方案:')
    print('    方案1: 使用HTTP服务器提供前端文件')
    print('    方案2: 修改API的CORS配置')
    print('    方案3: 检查前端API地址配置')
    
    print('\n  ✅ 立即执行:')
    print('    1. 启动HTTP服务器: python -m http.server 8080 -d frontend')
    print('    2. 访问: http://localhost:8080/index_unified_cockpit.html')
    print('    3. 这样可以避免file://协议的CORS限制')

if __name__ == "__main__":
    expert_api_diagnosis()
